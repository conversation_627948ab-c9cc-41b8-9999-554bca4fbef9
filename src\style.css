@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .bridge-card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  .bridge-title {
    @apply text-2xl font-bold text-bridge-blue mb-4;
  }
  .bridge-subtitle {
    @apply text-xl font-semibold text-bridge-light-blue mb-3;
  }
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: flex-start; /* 改为顶部对齐 */
  min-width: 320px;
  min-height: 100vh;
  padding: 1rem;
  box-sizing: border-box;
}

/* 移动端优化 */
@media (max-width: 768px) {
  body {
    padding: 0.5rem;
  }
  
  #app {
    padding: 1rem;
    width: 100%;
  }
  
  .bridge-card {
    padding: 1rem;
  }
  
  .bridge-title {
    font-size: 1.5rem;
  }
  
  .bridge-subtitle {
    font-size: 1.2rem;
  }
  
  button {
    padding: 0.8em 1em; /* 增大点击区域 */
    min-width: 44px; /* 满足WCAG触摸目标尺寸 */
    min-height: 44px;
  }
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
