<template>
  <div class="bridge-card">
    <h2 class="bridge-title">第二章 1♦️开叫及以后的应叫</h2>
    
    <!-- 一、1D开叫的应叫 -->
    <div class="mb-8">
      <h2 class="bridge-title">一、1♦️开叫的应叫</h2>
      <el-table :data="oneDiamondResponses" style="width: 100%" border>
        <el-table-column prop="bid" label="应叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.bid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="牌型/说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 二、1D后续叫牌演变 -->
    <div class="mb-8">
      <h2 class="bridge-title">二、1♦️后续叫牌演变</h2>
      
      <!-- 1D-1H后续 -->
      <div class="mb-8">
        <h3 class="bridge-subtitle">1. 1♦️—1♥️以后的叫牌</h3>
        <div v-for="(section, index) in oneDiamondOneHeart" :key="index" class="mb-6">
          <h4 class="bridge-minititle">{{ section.title }}</h4>
          <el-table :data="section.data" style="width: 100%" border>
            <el-table-column prop="sequence" label="叫牌进程" width="200">
              <template #default="{ row }">
                <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.sequence) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="牌型/说明">
              <template #default="{ row }">
                <span>{{ suitToEmoji(row.description) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 1D-1S后续 -->
      <div class="mb-8">
        <h3 class="bridge-subtitle">2. 1♦️—1♠️以后的叫牌</h3>
        <div v-for="(section, index) in oneDiamondOneSpade" :key="index" class="mb-6">
          <h4 class="bridge-minititle">{{ section.title }}</h4>
          <el-table :data="section.data" style="width: 100%" border>
            <el-table-column prop="sequence" label="叫牌进程" width="200">
              <template #default="{ row }">
                <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.sequence) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="牌型/说明">
              <template #default="{ row }">
                <span>{{ suitToEmoji(row.description) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

function suitToEmoji(str: string) {
  return str
    .replace(/C/g, '♣️')
    .replace(/D/g, '♦️')
    .replace(/H/g, '♥️')
    .replace(/S/g, '♠️')
}

const oneDiamondResponses = ref([
  { bid: 'Pass', points: '0-5点', description: '4~5张以上D，弱牌' },
  { bid: '1H', points: '6-11点', description: '4张H↑，或有更长低花；或12点↑，H/S为长套，4-4叫1H，5-5叫1S，逼叫' },
  { bid: '1S', points: '6-11点', description: '4张S↑，或有更长低花；或12点↑，H/S为长套，4-4叫1H，5-5叫1S，逼叫' },
  { bid: '1NT', points: '6-10点', description: '没有4张高花，可能有4~5张D，不逼叫' },
  { bid: '2C', points: '12点以上', description: '5张以上C，可能有4张高花，二盖一逼局' },
  { bid: '2D', points: '10点以上', description: '4张以上D，低花反加叫，逼叫' },
  { bid: '2H/2S', points: '4-6点', description: '6张以上H/S，阻击叫' },
  { bid: '2NT', points: '11-12点', description: '没有4张高花和5张以上D，邀叫' },
  { bid: '3C', points: '9-11点', description: '好的6张以上C，邀叫' },
  { bid: '3D', points: '4-8点', description: '5张D↑，无4张高花，阻击叫；6-8点时通常高花有单缺' },
  { bid: '3H/3S', points: '12-15点', description: '5张以上D，没有4张高花，H/S单缺，SPL，逼局' },
  { bid: '3NT', points: '13-15点', description: '没有4张高花' },
  { bid: '4C', points: '16点以上', description: '5张以上D，没有4张高花，C单缺，SPL，逼局' },
  { bid: '4D', points: '4-8点', description: '6张以上D，加重阻击，点力不高有牌型' },
  { bid: '4H/4S', points: '5-8点', description: '7张以上H/S' },
  { bid: '4NT', points: '18点以上', description: 'D为将牌的罗马关键张问叫' },
  { bid: '5C', points: '13-17点', description: '7张以上C' },
  { bid: '5D', points: '4-8点', description: '7张以上D' }
])

const oneDiamondOneHeart = ref([
  {
    title: '1♦️—1♥️ 开叫人再叫',
    data: [
      { sequence: '1S', description: '12-17点，4张S，不逼叫' },
      { sequence: '1NT', description: '12-14点，无4张高花；未叫花色可无止张' },
      { sequence: '2C', description: '12-17点，5张以上D＋4张C，不逼叫' },
      { sequence: '2D', description: '12-15点，6张以上D，无4张高花，不逼叫' },
      { sequence: '2H', description: '12-15点，4张H或3张H有单缺，不逼叫' },
      { sequence: '2S', description: '18-21点，5张以上D＋4张S，跳叫新花逼局' },
      { sequence: '2NT', description: '18-19点，可能有4张S，不逼叫' },
      { sequence: '3C', description: '18-21点，5张以上D＋4张C，跳叫新花逼局' },
      { sequence: '3D', description: '16-18点，6张以上D，无4张高花，邀叫' },
      { sequence: '3H', description: '15-17点，4张H，邀叫' },
      { sequence: '3S', description: '18-21点，4张H，S/C单缺，SPL，逼局' },
      { sequence: '3NT', description: '16-18点，坚固的6张以上D，未叫花色有止' },
      { sequence: '4D', description: '16-21点，6张以上D＋4张H，逼局' },
      { sequence: '4H', description: '18-21点，4张H' }
    ]
  },
  {
    title: '1♦️—1♥️—1S 应叫人再叫',
    data: [
      { sequence: 'Pass', description: '6-7点，3~4张S，愿意打4-3配合将牌' },
      { sequence: '1NT', description: '6-10点，无4张S，不逼叫' },
      { sequence: '2C', description: '12点以上，第四花色逼局，与C无关' },
      { sequence: '2D', description: '6-9点，4张以上D，无4张S，不逼叫' },
      { sequence: '2H', description: '6-9点，6张H，不逼叫' },
      { sequence: '2S', description: '8-9点，4张S，不逼叫' },
      { sequence: '2NT', description: '11-12点，均型，高花无配合，邀叫' },
      { sequence: '3C', description: '10-11点，5张以上H＋5张以上C，邀叫' },
      { sequence: '3D', description: '10-11点，4张以上D，邀叫' },
      { sequence: '3H', description: '10-11点，6张以上H，邀叫' },
      { sequence: '3S', description: '10-11点，4张S，邀叫' },
      { sequence: '3NT', description: '13-15点，止叫' },
      { sequence: '4C', description: '13点以上，4张S，C/D单缺，SPL，逼局' },
      { sequence: '4H', description: '10-12点，7张以上H，止叫' },
      { sequence: '4S', description: '12-15点，4张S，止叫' }
    ]
  }
])

const oneDiamondOneSpade = ref([
  {
    title: '1♦️—1♠️ 开叫人再叫',
    data: [
      { sequence: '1NT', description: '12-14点，未叫花色可无止张，不逼叫' },
      { sequence: '2C', description: '12-17点，5张以上D＋4张以上C，不逼叫' },
      { sequence: '2D', description: '12-15点，6张以上D，不逼叫' },
      { sequence: '2H', description: '16-21点，5张以上D＋4张H，逆叫，逼叫一轮' },
      { sequence: '2S', description: '12-15点，4张S，或3张S有单缺，不逼叫' },
      { sequence: '2NT', description: '18-19点，跳叫2NT，可能有4张H，不逼叫' },
      { sequence: '3C', description: '18-21点，跳叫新花，至少好的5-4套，逼局' },
      { sequence: '3D', description: '16-18点，6张以上D，无4张高花，不逼叫' },
      { sequence: '3H', description: '18-21点，4张S，所叫花色H单缺，SPL，逼叫' },
      { sequence: '3S', description: '16-18点，4张S，邀叫' },
      { sequence: '3NT', description: '16-18点，坚固的6张以上D，未叫花色有止张' },
      { sequence: '4C', description: '18-21点，4张S，所叫花色C单缺，SPL，逼叫' },
      { sequence: '4D', description: '16-21点，6张以上D＋4张S，逼局' },
      { sequence: '4S', description: '18-21点，4张S' }
    ]
  },
  {
    title: '1♦️—1♠️—2C 应叫人再叫',
    data: [
      { sequence: 'Pass', description: '6-9点，C长于D，没有成局的可能' },
      { sequence: '2D', description: '6-9点，4~5张S，D长于C，不逼叫' },
      { sequence: '2H', description: '12点以上，第四花色逼局，与H无关' },
      { sequence: '2S', description: '6-9点，6张以上S，不逼叫' },
      { sequence: '2NT', description: '11-12点，均型牌，邀叫' },
      { sequence: '3C', description: '8-10点，4张以上C，不逼叫' },
      { sequence: '3D', description: '10-11点，3张以上D，邀叫' },
      { sequence: '3H', description: '10-11点，5张S↑＋5张H↑，邀叫' },
      { sequence: '3S', description: '10-11点，6张以上S，邀叫' },
      { sequence: '3NT', description: '12-17点，止叫' },
      { sequence: '4C', description: '12点以上，4张以上C，逼局' },
      { sequence: '4D', description: '12点以上，4张以上D，逼局' }
    ]
  }
])

const getPointTagType = (points: string) => {
  if (points.includes('10点以上') || points.includes('11-12') || points.includes('18点以上') || points.includes('18-19') || points.includes('20-21')) return 'success'
  if (points.includes('6-11') || points.includes('6-10') || points.includes('12-14') || points.includes('12-15') || points.includes('13-15') || points.includes('15-17') || points.includes('5-8')) return 'info'
  if (points.includes('0-5') || points.includes('4-8')) return 'warning'
  return 'default'
}
</script>

<style scoped>
.bridge-card {
  @apply bg-white rounded-lg shadow-md p-6;
}
.bridge-title {
  @apply text-2xl font-bold text-bridge-blue mb-6;
}
.bridge-subtitle {
  @apply text-xl font-semibold text-bridge-blue mb-4;
}
.bridge-minititle {
  @apply text-lg font-bold text-bridge-blue mb-4;
}
.prose {
  @apply text-gray-700 leading-relaxed;
}
.prose p {
  @apply mb-4;
}

    <!-- 三、双路重询斯台曼 -->
    <div class="mb-8">
      <h2 class="bridge-title">三、双路重询斯台曼</h2>
      
      <!-- 1D-1H-1NT 后续 -->
      <div class="mb-8">
        <h3 class="bridge-subtitle">1♦️—1♥️—1NT 后续</h3>
        <el-table :data="processedStaymanOneHeart" style="width: 100%" border>
          <el-table-column
            prop="displayBid"
            label="应叫"
            width="120"
          />
          <el-table-column
            prop="displayDescription"
            label="牌型/说明"
          />
        </el-table>
      </div>

      <!-- 1D-1S-1NT 后续 -->
      <div class="mb-8">
        <h3 class="bridge-subtitle">1♦️—1♠️—1NT 后续</h3>
        <el-table :data="staymanOneSpade" style="width: 100%" border>
          <el-table-column prop="bid" label="应叫" width="120">
            <template #default="{ row }">
              <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.bid) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="牌型/说明">
            <template #default="{ row }">
              <span>{{ suitToEmoji(row.description) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const convertToEmoji = (text: string) => {
  return text
    .replace(/C/g, '♣️')
    .replace(/D/g, '♦️')
    .replace(/H/g, '♥️')
    .replace(/S/g, '♠️')
}

const processedStaymanOneHeart = computed(() => {
  return rawStaymanOneHeart.map(item => ({
    displayBid: convertToEmoji(item.bid),
    displayDescription: convertToEmoji(item.description),
    originalBid: item.bid,
    originalDescription: item.description
  }))
})

const processedStaymanOneSpade = computed(() => {
  return rawStaymanOneSpade.map(item => ({
    displayBid: convertToEmoji(item.bid),
    displayDescription: convertToEmoji(item.description),
    originalBid: item.bid,
    originalDescription: item.description
  }))
})

const rawStaymanOneHeart = [
  { bid: 'Pass', description: '6-9点，不符合其他叫品' },
  { bid: '2C', description: '6-9点，要求同伴叫2D；后续Pass=4张以上D，示弱' },
  { bid: '2D', description: '10-12点，要求同伴叫2D；后续选择自然叫品，邀叫' },
  { bid: '2H', description: '13点以上，多数进局牌起步叫品，与D无关，逼局' },
  { bid: '2S', description: '6-9点，6张以上H，不逼叫' },
  { bid: '2NT', description: '13点以上，5张H＋4张S，逼局' },
  { bid: '3C/3D', description: '6-9点，要求同伴叫3C；后续Pass=6张以上C，示弱' },
  { bid: '3H', description: '13点以上，5张H＋5张C/D，逼局' },
  { bid: '3NT', description: '13-17点，不满足其他进局条件，止叫' },
  { bid: '4NT', description: '18-19点，满贯邀叫' }
]

const rawStaymanOneSpade = [
  { bid: 'Pass', description: '6-9点，不符合其他叫品' },
  { bid: '2C', description: '6-9点，要求同伴叫2D；后续Pass=4张以上D，示弱' },
  { bid: '2D', description: '10-12点，要求同伴叫2D；后续选择自然叫品，邀叫' },
  { bid: '2H', description: '13点以上，多数进局牌起步叫品，与D无关，逼局' },
  { bid: '2S', description: '6-9点，5张S＋4张以上H，不逼叫' },
  { bid: '2NT', description: '6-9点，6张以上S，不逼叫' },
  { bid: '3C/3D', description: '6-9点，要求同伴叫3C；后续Pass=6张以上C，示弱' },
  { bid: '3H', description: '13点以上，5张以上S＋5张以上C/D/H，逼局' },
  { bid: '3S', description: '13点以上，半坚固的6张以上S，逼局' },
  { bid: '3NT', description: '13-17点，不满足其他进局条件，止叫' },
  { bid: '4NT', description: '18-19点，满贯邀叫' }
]

const staymanOneHeart = computed(() => processBids(rawStaymanOneHeart))
const staymanOneSpade = computed(() => processBids(rawStaymanOneSpade))
</script>

<style scoped>
.bridge-card {
  @apply bg-white rounded-lg shadow-md p-6;
}
.bridge-title {
  @apply text-2xl font-bold text-bridge-blue mb-6;
}
.bridge-subtitle {
  @apply text-xl font-semibold text-bridge-blue mb-4;
}
.bridge-minititle {
  @apply text-lg font-bold text-bridge-blue mb-4;
}
.prose {
  @apply text-gray-700 leading-relaxed;
}
.prose p {
  @apply mb-4;
}
</style>
